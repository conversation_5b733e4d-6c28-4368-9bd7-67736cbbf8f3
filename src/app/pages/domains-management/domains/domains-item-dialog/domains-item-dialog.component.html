<h3 mat-dialog-title class="no-margin-top mb-20">
  {{ title | translate }}
</h3>
<div mat-dialog-content>
  <form [formGroup]="form">
    <mat-form-field appearance="outline" class="width100">
      <mat-label>{{'DOMAINS.GRID.name' | translate}}</mat-label>
      <input type="text" matInput trimValue [formControl]="domainControl">
      <mat-error *ngIf="domainControl.hasError('required') && domainControl.touched">
        {{'VALIDATION.required' | translate}}
      </mat-error>
    </mat-form-field>
    <mat-form-field appearance="outline" *ngIf="domainType === 'static'" class="width100">
      <mat-label>{{'DOMAINS.GRID.type' | translate}}</mat-label>
      <mat-select [formControl]="staticTypeControl">
        <mat-option *ngFor="let staticType of staticTypes" [value]="staticType.id">
          {{staticType.displayName | translate}}
        </mat-option>
      </mat-select>
      <mat-error *ngIf="staticTypeControl.hasError('required') && staticTypeControl.touched">
        {{'VALIDATION.required' | translate}}
      </mat-error>
    </mat-form-field>
    <mat-form-field appearance="outline" *ngIf="domainType === 'dynamic'" class="width100">
      <mat-label>{{'DOMAINS.GRID.gameServer' | translate}}</mat-label>
      <mat-select [formControl]="environmentControl">
        <mat-option *ngFor="let server of gameServers" [value]="server">
          {{server}}
        </mat-option>
      </mat-select>
      <mat-error *ngIf="environmentControl.hasError('required') && environmentControl.touched">
        {{'VALIDATION.required' | translate}}
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="width100">
      <mat-label>{{ 'DOMAINS.GRID.description' | translate }}:</mat-label>
      <textarea  matInput trimValue [formControl]="descriptionControl" [rows]="5"></textarea>
      <mat-error *ngIf="descriptionControl.hasError('required') && descriptionControl.touched">
        {{'VALIDATION.required' | translate}}
      </mat-error>
    </mat-form-field>
  </form>
</div>
<div mat-dialog-actions fxLayout="row" fxLayoutAlign="end center">
  <button mat-button color="primary" class="mat-button-md" (click)="onNoClick()">{{ 'DIALOG.cancel' | translate }}</button>
  <button mat-flat-button color="primary" class="mat-button-md" (click)="submit()">{{ 'DIALOG.save' | translate }}</button>
</div>
