import { SwuiGridField } from '@skywind-group/lib-swui';

const STATUS__CLASS_LIST = {
  active: 'sw-chip sw-chip-green',
  unavailable: 'sw-chip sw-chip-gray',
};

export const DOMAIN_NAME_SCHEMA: SwuiGridField =
{
  field: 'domain',
  title: 'DOMAINS.GRID.domain',
  type: 'string',
  isList: true,
  isViewable: false,
  isSortable: false,
  isFilterable: true
};

export const DOMAIN_SCHEMA: SwuiGridField[] = [
  {
    field: 'status',
    title: 'DOMAINS.GRID.status',
    type: 'select',
    isList: true,
    data: [
      { id: 'active', code: 'active', displayName: 'DOMAINS.GRID.statusActive' },
      { id: 'suspended', code: 'suspended', displayName: 'DOMAINS.GRID.statusInactive' }
    ],
    isViewable: true,
    isSortable: true,
    isFilterable: true,
    td: {
      type: 'calc',
      useTranslate: false,
      titleFn: (row: any, schema: SwuiGridField) => row[schema.field],
      classFn: (row: any, schema: SwuiGridField) => {
        return STATUS__CLASS_LIST[row[schema.field]] || 'sw-chip sw-chip-blue';
      }
    },
  },
  {
    field: 'description',
    title: 'DOMAINS.GRID.description',
    type: 'string',
    isList: true,
    isViewable: true,
    isSortable: false,
    isFilterable: false,
    td: {
      type: 'string',
      truncate: {
        maxLength: 75,
        isEllipsis: true,
      }
    },
  },
  {
    field: 'createdAt',
    title: 'DOMAINS.GRID.created',
    type: 'datetimerange',
    dataSource: '',
    isList: true,
    isViewable: false,
    isSortable: true,
    td: {
      type: 'timestamp',
      nowrap: true
    },
  },
];
